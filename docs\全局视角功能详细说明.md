# 知识图谱全局视角功能详细说明

## 概述

本文档详细说明知识图谱中鼠标左键双击进入全局视角以及进入全局视角后自动旋转功能的实现机制和配置方法。

## 功能架构图

```mermaid
graph TD
    A[用户双击鼠标左键] --> B[事件处理器检测双击]
    B --> C[throttleHandleEnterGlobalView]
    C --> D[计算目标视角位置]
    D --> E[调用enterGlobalView函数]
    E --> F[清理现有动画]
    F --> G[切换视图模式]
    G --> H[隐藏聚焦装饰元素]
    H --> I[启用节点呼吸动画]
    I --> J[执行视角切换动画]
    J --> K[动画完成后启用自动旋转]
    K --> L[监听鼠标移动事件]
    L --> M{检测到鼠标移动?}
    M -->|是| N[停止自动旋转]
    M -->|否| O[3秒后恢复自动旋转]
    N --> P[等待鼠标停止移动]
    P --> O
```

## 核心实现机制

### 1. 双击事件处理

#### 事件绑定
```typescript
// 文件位置: src/components/ksgMap/config/event.ts (第283行)
containerEle.addEventListener("dblclick", throttleHandleEnterGlobalView);
```

#### 双击处理函数
```typescript
// 文件位置: src/components/ksgMap/config/event.ts (第206-253行)
function handleEnterGlobalView(_: MouseEvent) {
  // 防止重复进入全局视角
  if (ctx.focusStack![ctx.focusStack!.length - 1] === "null") return;
  
  // 获取当前相机Y轴位置
  const { y: cameraY } = ctx.camera?.position!;
  
  // 计算所有层级的Y坐标
  const peerLevelsY = Object.keys(ctx.graph!.idLevelMap).map(
    (level) =>
      ctx.graph?.getPointById(ctx.graph!.idLevelMap[Number(level)][0]!)
        ?.coordinate[1]
  );

  // 计算最终视角位置
  const rang = ctx.levelSpace! / 2;
  let resultY = peerLevelsY.length > 1
    ? peerLevelsY?.find(
        (item) => cameraY > item! - rang && cameraY < item! + rang
      )
    : peerLevelsY[0];

  // 处理边界情况
  if (peerLevelsY.length > 1) {
    if (!resultY && cameraY > peerLevelsY![0]!) {
      resultY = peerLevelsY![1]!;
    } else if (!resultY && cameraY < peerLevelsY![peerLevelsY?.length! - 1]!) {
      resultY = peerLevelsY![peerLevelsY?.length! - 2]!;
    }
  }

  // 计算方向向量和缩放倍率
  const offset = ctx.camera?.position
    .clone()
    .sub(new Vector3(0, resultY!, 0));
  offset?.multiplyScalar(2);

  // 隐藏聚焦标签并进入全局视角
  focusLabel.hide();
  enterGlobalView([offset!.x, resultY!, offset!.z])?.then(() => {
    // 启用自动旋转
    ctx.controls!.autoRotate = true;
    ctx.controls!.autoRotateSpeed = 0.02;
    ctx.controls!.enabled = true;
    enableHover = true;
    wrapperEle!.addEventListener("mousemove", handleMoveEvent);
  });
}
```

#### 防抖处理
```typescript
// 文件位置: src/components/ksgMap/config/event.ts (第254行)
const throttleHandleEnterGlobalView = throttle(handleEnterGlobalView, 500);
```

### 2. 全局视角切换核心逻辑

#### enterGlobalView函数
```typescript
// 文件位置: src/components/ksgMap/core/enterGlobalView.ts (第37-70行)
export default function enterGlobalView(to: [number, number, number]) {
  // 步骤1：清理所有正在进行的补间动画
  TWEEN.removeAll();

  // 步骤2：防止重复切换到相同视图模式
  if (ctx.viewMode == VIEW_MODE.GLOBAL_VIEW) return;

  // 步骤3：更新视图模式状态
  ctx.viewMode = VIEW_MODE.GLOBAL_VIEW;

  // 步骤4：隐藏聚焦装饰元素
  focusCrust.hide();

  // 步骤5：启用所有节点的呼吸动画
  ctx.pointsMesh!.breathAnimationSwitch();

  // 步骤6：更新历史栈
  ctx.focusStack?.push("null");

  // 步骤7：处理聚焦连线的退出动画
  if (ctx.focusLine) {
    ctx.focusLine.dispose();
    lineLeaveAnimation(ctx.focusLine).then(() => {
      ctx.viewGroup!.remove(ctx.focusLine!);
    });
  }

  // 步骤8：执行视角切换动画
  return enterGlobalAnimation(ctx.controls!, to);
}
```

#### 视角切换动画
```typescript
// 文件位置: src/components/ksgMap/animation/enterGlobal.ts (第10-42行)
export function enterGlobalAnimation(
  controls: KsgControls,
  to: [number, number, number]
) {
  return new Promise((resolve) => {
    new Tween({
      x: controls.object.position.x,
      y: controls.object.position.y,
      z: controls.object.position.z,
      targetX: controls.target.x,
      targetY: controls.target.y,
      targetZ: controls.target.z,
    })
      .to(
        {
          x: to[0],
          y: to[1] + 5,
          z: to[2],
          targetX: 0,
          targetY: to[1],
          targetZ: 0,
        },
        400  // 动画持续时间：400毫秒
      )
      .onUpdate(({ x, y, z, targetY, targetX, targetZ }) => {
        controls.object.position.set(x, y, z);
        controls.target.set(targetX, targetY, targetZ);
      })
      .easing(Easing.Quartic.InOut)  // 缓动函数
      .start()
      .onComplete(resolve);
  });
}
```

### 3. 自动旋转实现机制

#### 自动旋转核心算法
```typescript
// 文件位置: src/components/ksgMap/core/KsgControls.ts (第1132-1142行)
autoRotateUpdate(deltaTime: number) {
  if (this.autoRotate) {
    // 计算相机相对于目标的偏移向量
    const offset1 = this.object.position.clone().sub(this.target);
    
    // 计算旋转角度（基于时间和速度）
    const angle = this.autoRotateSpeed * deltaTime!;
    
    // 创建绕Y轴旋转的矩阵
    const rotationMatrix = new Matrix4().makeRotationY(angle);
    
    // 应用旋转变换
    offset1.applyMatrix4(rotationMatrix);
    
    // 更新相机位置并保持朝向目标
    this.object.position.copy(this.target).add(offset1);
    this.object.lookAt(this.target);
  }
}
```

#### 自动旋转控制参数
```typescript
// 文件位置: src/components/ksgMap/core/KsgControls.ts (第240-241行)
this.autoRotate = false;       // 默认关闭自动旋转
this.autoRotateSpeed = 2.0;    // 默认自动旋转速度
```

### 4. 鼠标移动检测与自动旋转控制

#### 鼠标移动事件处理器
```typescript
// 文件位置: src/components/ksgMap/utils/globalViewEvent.ts (第11-52行)
export function createMouseMoveEvent(
  onMove: () => void,
  onMoveEnd: () => void,
  moveEndDelay: number = 100
) {
  let timer: number | null | NodeJS.Timer = null;
  
  function handleMoveEvent(event: MouseEvent) {
    // 首次移动立即触发onMove
    if (!timer) onMove();
    
    // 清除之前的定时器，重新开始计时
    clearTimeout(timer as number);
    
    // 设置新的定时器，在指定延迟后触发移动结束事件
    timer = setTimeout(() => {
      onMoveEnd();
      timer = null;
    }, moveEndDelay);
  }

  function clear() {
    clearTimeout(timer as number);
    timer = null;
  }
  
  return {
    handleMoveEvent,
    clear,
  };
}
```

#### 自动旋转启停控制
```typescript
// 文件位置: src/components/ksgMap/config/event.ts (第258-272行)
function onMove() {
  if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && ctx.controls!.autoRotate) {
    ctx.controls!.autoRotate = false;  // 鼠标移动时停止自动旋转
  }
}

function onMoveEnd() {
  if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && !ctx.controls!.autoRotate) {
    ctx.controls!.autoRotate = true;   // 鼠标停止移动后恢复自动旋转
  }
}

const { handleMoveEvent, clear: clearMoveEvent } = createMouseMoveEvent(
  onMove,
  onMoveEnd,
  3000  // 3秒等待时间
);
```

## 配置参数说明

### 1. 双击事件配置

| 参数名 | 默认值 | 说明 | 位置 |
|--------|--------|------|------|
| 防抖延迟 | 500ms | 防止重复触发双击事件 | event.ts:254 |

### 2. 视角切换动画配置

| 参数名 | 默认值 | 说明 | 位置 |
|--------|--------|------|------|
| 动画持续时间 | 400ms | 视角切换动画的持续时间 | enterGlobal.ts:32 |
| 缓动函数 | Quartic.InOut | 动画的缓动效果 | enterGlobal.ts:38 |
| 相机高度偏移 | +5 | 相机相对于目标的高度偏移 | enterGlobal.ts:26 |

### 3. 自动旋转配置

| 参数名 | 默认值 | 说明 | 位置 |
|--------|--------|------|------|
| autoRotate | false | 是否启用自动旋转 | KsgControls.ts:240 |
| autoRotateSpeed | 2.0 | 自动旋转速度（默认值） | KsgControls.ts:241 |
| 实际使用速度 | 0.02 | 进入全局视角时设置的速度 | event.ts:248 |

### 4. 鼠标检测配置

| 参数名 | 默认值 | 说明 | 位置 |
|--------|--------|------|------|
| 无操作检测时间 | 3000ms | 鼠标停止移动后多久恢复自动旋转 | event.ts:271 |

## 速度设置方法

### 1. 修改自动旋转速度

#### 方法一：修改默认速度
```typescript
// 文件位置: src/components/ksgMap/core/KsgControls.ts (第241行)
this.autoRotateSpeed = 2.0;  // 修改此值调整默认速度
```

#### 方法二：修改进入全局视角时的速度
```typescript
// 文件位置: src/components/ksgMap/config/event.ts (第248行)
ctx.controls!.autoRotateSpeed = 0.02;  // 修改此值调整实际使用速度
```

#### 方法三：运行时动态调整
```typescript
// 在代码中动态调整
ctx.controls!.autoRotateSpeed = 0.05;  // 设置新的旋转速度
```

### 2. 速度值说明

- **速度单位**: 弧度/秒
- **正值**: 顺时针旋转
- **负值**: 逆时针旋转
- **推荐范围**: 0.01 - 0.1 (过快会影响用户体验)

### 3. 速度对比

| 速度值 | 旋转效果 | 适用场景 |
|--------|----------|----------|
| 0.01 | 非常慢 | 细致观察 |
| 0.02 | 慢速 | 默认推荐 |
| 0.05 | 中等 | 快速浏览 |
| 0.1 | 快速 | 演示展示 |

### 4. 修改无操作检测时间

```typescript
// 文件位置: src/components/ksgMap/config/event.ts (第271行)
const { handleMoveEvent, clear: clearMoveEvent } = createMouseMoveEvent(
  onMove,
  onMoveEnd,
  3000  // 修改此值调整检测时间（毫秒）
);
```

## 关键文件列表

### 核心实现文件
- `src/components/ksgMap/core/enterGlobalView.ts` - 全局视角切换核心逻辑
- `src/components/ksgMap/animation/enterGlobal.ts` - 视角切换动画实现
- `src/components/ksgMap/core/KsgControls.ts` - 相机控制器和自动旋转实现
- `src/components/ksgMap/config/event.ts` - 事件系统和双击处理
- `src/components/ksgMap/utils/globalViewEvent.ts` - 鼠标移动检测器

### 相关类型定义
- `types/components/ksgMap/core/KsgControls.d.ts` - 控制器类型定义
- `types/components/ksgMap/animation/enterGlobal.d.ts` - 动画函数类型定义

## 常见问题与解决方案

### 1. 自动旋转速度过快或过慢
**问题**: 自动旋转速度不符合预期
**解决方案**:
```typescript
// 调整自动旋转速度
ctx.controls!.autoRotateSpeed = 0.03; // 根据需要调整数值
```

### 2. 双击响应延迟
**问题**: 双击后延迟进入全局视角
**解决方案**:
```typescript
// 减少防抖延迟时间
const throttleHandleEnterGlobalView = throttle(handleEnterGlobalView, 300);
```

### 3. 鼠标停止后自动旋转恢复时间过长
**问题**: 鼠标停止移动后等待时间过长
**解决方案**:
```typescript
// 减少等待时间
const { handleMoveEvent, clear: clearMoveEvent } = createMouseMoveEvent(
  onMove,
  onMoveEnd,
  2000  // 从3000ms减少到2000ms
);
```

### 4. 视角切换动画过快或过慢
**问题**: 进入全局视角的动画速度不合适
**解决方案**:
```typescript
// 调整动画持续时间
.to({...}, 600)  // 从400ms增加到600ms
```

## 扩展功能建议

### 1. 可配置的旋转方向
```typescript
// 添加旋转方向配置
autoRotateClockwise: boolean = true;

// 在autoRotateUpdate中应用
const angle = this.autoRotateSpeed * deltaTime * (this.autoRotateClockwise ? 1 : -1);
```

### 2. 可配置的旋转轴
```typescript
// 支持不同轴向的旋转
autoRotateAxis: 'x' | 'y' | 'z' = 'y';

// 根据轴向创建不同的旋转矩阵
const rotationMatrix = this.autoRotateAxis === 'y'
  ? new Matrix4().makeRotationY(angle)
  : this.autoRotateAxis === 'x'
  ? new Matrix4().makeRotationX(angle)
  : new Matrix4().makeRotationZ(angle);
```

### 3. 渐进式速度调整
```typescript
// 添加速度渐变效果
autoRotateAcceleration: number = 0.001;

// 在更新中应用渐变
if (this.autoRotate) {
  this.currentRotateSpeed = Math.min(
    this.currentRotateSpeed + this.autoRotateAcceleration,
    this.autoRotateSpeed
  );
}
```

## 性能优化建议

### 1. 减少不必要的计算
- 在自动旋转停止时避免执行旋转计算
- 使用对象池复用Vector3和Matrix4对象

### 2. 优化事件监听
- 使用passive事件监听器提高性能
- 在组件卸载时及时清理事件监听器

### 3. 动画优化
- 使用requestAnimationFrame确保动画流畅
- 在页面不可见时暂停动画

## 总结

全局视角功能通过精心设计的事件处理、动画系统和自动旋转机制，为用户提供了流畅的知识图谱浏览体验。通过调整相关配置参数，可以根据具体需求优化用户交互体验。

该功能的核心优势：
1. **流畅的视角切换**: 使用Tween.js实现平滑的相机移动动画
2. **智能的自动旋转**: 基于用户操作自动启停，提供沉浸式体验
3. **灵活的配置系统**: 支持多种参数调整，适应不同使用场景
4. **良好的性能表现**: 优化的事件处理和动画机制，确保流畅运行
## 全局视角功能配置示例

### 1. 基础配置

#### 调整自动旋转速度
```typescript
// 文件: src/components/ksgMap/config/event.ts
// 第248行，进入全局视角后设置的旋转速度
ctx.controls!.autoRotateSpeed = 0.02; // 默认值，可调整

// 推荐配置：
// 0.01 - 慢速旋转，适合细致观察
// 0.02 - 标准速度，推荐使用
// 0.05 - 快速旋转，适合演示
// 0.1  - 很快，可能影响用户体验
```

#### 调整鼠标无操作检测时间
```typescript
// 文件: src/components/ksgMap/config/event.ts
// 第271行，鼠标停止移动后多久恢复自动旋转
const { handleMoveEvent, clear: clearMoveEvent } = createMouseMoveEvent(
  onMove,
  onMoveEnd,
  3000  // 3秒，可调整为1000-5000ms
);
```

#### 调整双击防抖时间
```typescript
// 文件: src/components/ksgMap/config/event.ts
// 第254行，防止重复触发双击
const throttleHandleEnterGlobalView = throttle(handleEnterGlobalView, 500);
// 可调整为200-1000ms
```

#### 调整视角切换动画时间
```typescript
// 文件: src/components/ksgMap/animation/enterGlobal.ts
// 第32行，视角切换动画持续时间
.to({...}, 400)  // 400毫秒，可调整为200-1000ms
```

### 2. 高级配置

#### 自定义旋转方向
```typescript
// 在KsgControls类中添加属性
autoRotateClockwise: boolean = true; // true=顺时针，false=逆时针

// 修改autoRotateUpdate方法
autoRotateUpdate(deltaTime: number) {
  if (this.autoRotate) {
    const offset1 = this.object.position.clone().sub(this.target);
    // 根据方向调整角度
    const angle = this.autoRotateSpeed * deltaTime * (this.autoRotateClockwise ? 1 : -1);
    const rotationMatrix = new Matrix4().makeRotationY(angle);
    offset1.applyMatrix4(rotationMatrix);
    this.object.position.copy(this.target).add(offset1);
    this.object.lookAt(this.target);
  }
}
```

#### 渐进式速度调整
```typescript
// 在KsgControls类中添加属性
currentRotateSpeed: number = 0;
autoRotateAcceleration: number = 0.001;

// 修改autoRotateUpdate方法实现渐进加速
autoRotateUpdate(deltaTime: number) {
  if (this.autoRotate) {
    // 渐进加速到目标速度
    this.currentRotateSpeed = Math.min(
      this.currentRotateSpeed + this.autoRotateAcceleration,
      this.autoRotateSpeed
    );
    
    const offset1 = this.object.position.clone().sub(this.target);
    const angle = this.currentRotateSpeed * deltaTime;
    const rotationMatrix = new Matrix4().makeRotationY(angle);
    offset1.applyMatrix4(rotationMatrix);
    this.object.position.copy(this.target).add(offset1);
    this.object.lookAt(this.target);
  } else {
    // 停止时重置速度
    this.currentRotateSpeed = 0;
  }
}
```

### 3. 运行时动态调整

#### 通过代码动态调整速度
```typescript
// 获取控制器实例
const controls = ctx.controls;

// 动态调整旋转速度
controls.autoRotateSpeed = 0.05; // 设置新速度

// 动态启停自动旋转
controls.autoRotate = true;  // 启用
controls.autoRotate = false; // 停用
```

#### 通过UI控制面板调整
```typescript
// 创建配置对象
const globalViewConfig = {
  autoRotateSpeed: 0.02,
  mouseDetectionDelay: 3000,
  animationDuration: 400,
  doubleClickThrottle: 500
};

// 应用配置的函数
function applyGlobalViewConfig(config: typeof globalViewConfig) {
  // 更新自动旋转速度
  if (ctx.controls) {
    ctx.controls.autoRotateSpeed = config.autoRotateSpeed;
  }
  
  // 重新创建鼠标检测器（需要重新绑定事件）
  // 注意：这需要在事件初始化时实现配置支持
}
```

### 4. 性能优化配置

#### 降低更新频率
```typescript
// 在渲染循环中添加帧率控制
let lastUpdateTime = 0;
const updateInterval = 16; // 约60FPS

function render(currentTime: number) {
  if (currentTime - lastUpdateTime >= updateInterval) {
    controls.autoRotateUpdate((currentTime - lastUpdateTime) / 1000);
    lastUpdateTime = currentTime;
  }
  requestAnimationFrame(render);
}
```

#### 页面不可见时暂停
```typescript
// 监听页面可见性变化
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    // 页面不可见时暂停自动旋转
    if (ctx.controls && ctx.controls.autoRotate) {
      ctx.controls.autoRotate = false;
      ctx.controls._wasAutoRotating = true; // 记录状态
    }
  } else {
    // 页面可见时恢复自动旋转
    if (ctx.controls && ctx.controls._wasAutoRotating) {
      ctx.controls.autoRotate = true;
      ctx.controls._wasAutoRotating = false;
    }
  }
});
```

### 5. 常用配置组合

#### 演示模式（快速浏览）
```typescript
const demoConfig = {
  autoRotateSpeed: 0.08,      // 较快的旋转速度
  mouseDetectionDelay: 2000,  // 较短的检测时间
  animationDuration: 300,     // 较快的切换动画
  doubleClickThrottle: 300    // 较快的响应
};
```

#### 细致观察模式
```typescript
const detailConfig = {
  autoRotateSpeed: 0.01,      // 很慢的旋转速度
  mouseDetectionDelay: 5000,  // 较长的检测时间
  animationDuration: 600,     // 较慢的切换动画
  doubleClickThrottle: 500    // 标准响应
};
```

#### 标准模式（推荐）
```typescript
const standardConfig = {
  autoRotateSpeed: 0.02,      // 标准旋转速度
  mouseDetectionDelay: 3000,  // 标准检测时间
  animationDuration: 400,     // 标准切换动画
  doubleClickThrottle: 500    // 标准响应
};
```

### 6. 调试和测试

#### 添加调试信息
```typescript
// 在autoRotateUpdate中添加调试输出
autoRotateUpdate(deltaTime: number) {
  if (this.autoRotate) {
    console.log(`Auto rotate: speed=${this.autoRotateSpeed}, delta=${deltaTime}`);
    // ... 原有代码
  }
}
```

#### 性能监控
```typescript
// 监控旋转性能
let rotationCount = 0;
let lastLogTime = Date.now();

autoRotateUpdate(deltaTime: number) {
  if (this.autoRotate) {
    rotationCount++;
    const now = Date.now();
    if (now - lastLogTime > 1000) { // 每秒输出一次
      console.log(`Rotation FPS: ${rotationCount}`);
      rotationCount = 0;
      lastLogTime = now;
    }
    // ... 原有代码
  }
}
```

## 配置文件模板

```typescript
// globalViewConfig.ts
export interface GlobalViewConfig {
  autoRotateSpeed: number;
  mouseDetectionDelay: number;
  animationDuration: number;
  doubleClickThrottle: number;
  autoRotateClockwise: boolean;
  enableAcceleration: boolean;
  accelerationRate: number;
}

export const defaultConfig: GlobalViewConfig = {
  autoRotateSpeed: 0.02,
  mouseDetectionDelay: 3000,
  animationDuration: 400,
  doubleClickThrottle: 500,
  autoRotateClockwise: true,
  enableAcceleration: false,
  accelerationRate: 0.001
};

export const presets = {
  demo: {
    ...defaultConfig,
    autoRotateSpeed: 0.08,
    mouseDetectionDelay: 2000,
    animationDuration: 300
  },
  detail: {
    ...defaultConfig,
    autoRotateSpeed: 0.01,
    mouseDetectionDelay: 5000,
    animationDuration: 600
  }
};
```

通过以上配置，您可以根据具体需求灵活调整全局视角功能的各项参数，以获得最佳的用户体验。
