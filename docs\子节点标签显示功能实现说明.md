# 子节点标签显示功能实现说明

## 功能概述

当用户点击知识图谱中的节点进入聚焦状态时，系统不仅会显示聚焦节点的标签，还会同时显示该节点所有子元素的标签，提供更丰富的信息展示和更好的用户体验。

## 问题分析

### 原有实现的局限性

1. **只显示聚焦节点标签**：原系统只通过 `focusLabel.display()` 显示聚焦节点的标签
2. **子节点缺少标签**：子节点只有高亮效果（通过 `toggleFocusChildren()`），但没有对应的标签显示
3. **标签管理不完善**：系统中只有两个全局标签实例（`hoverLabel` 和 `focusLabel`），无法管理多个子节点标签

### 根本原因

```mermaid
graph TD
    A[用户点击节点] --> B[进入聚焦状态]
    B --> C[显示聚焦节点标签]
    B --> D[高亮子节点]
    D --> E[❌ 缺失：子节点标签显示]
    
    style E fill:#ffcccc
```

## 解决方案设计

### 整体架构

```mermaid
graph TD
    A[FocusChildrenLabelManager] --> B[标签生命周期管理]
    A --> C[多标签实例管理]
    A --> D[场景集成]
    
    B --> B1[显示子节点标签]
    B --> B2[隐藏子节点标签]
    B --> B3[清理子节点标签]
    
    C --> C1[Map存储标签实例]
    C --> C2[避免重复创建]
    C --> C3[批量操作支持]
    
    D --> D1[添加到3D场景]
    D --> D2[从场景移除]
    D --> D3[位置同步更新]
```

### 核心组件

#### 1. FocusChildrenLabelManager 类

**职责**：
- 管理聚焦状态下所有子节点的标签显示
- 提供标签的创建、显示、隐藏和清理功能
- 确保标签的生命周期管理和内存释放
- 与聚焦系统协调工作，避免标签冲突

**核心方法**：
```typescript
class FocusChildrenLabelManager {
  // 显示子节点标签
  showChildrenLabels(childrenPoints: Point[]): void
  
  // 隐藏所有子节点标签
  hideChildrenLabels(): void
  
  // 清理所有子节点标签
  clearChildrenLabels(): void
  
  // 更新标签位置
  updateChildLabelPosition(pointId: string, newPosition: [number, number, number]): void
  
  // 启用/禁用功能
  setEnabled(enabled: boolean): void
}
```

#### 2. 集成点修改

**renderData.ts**：
```typescript
// 在聚焦渲染开始时清理旧标签
focusChildrenLabelManager.clearChildrenLabels();

// 在连线动画完成后显示新的子节点标签
focusChildrenLabelManager.showChildrenLabels(lastFocusChildPoints);
```

**enterGlobalView.ts**：
```typescript
// 进入全局视图时清理子节点标签
focusChildrenLabelManager.clearChildrenLabels();
```

**event.ts**：
```typescript
// 在各种状态切换时清理子节点标签
focusChildrenLabelManager.clearChildrenLabels();
```

## 实现流程

### 1. 点击节点进入聚焦状态

```mermaid
sequenceDiagram
    participant User as 用户
    participant ClickEvent as 点击事件
    participant EnterFocus as 聚焦处理
    participant RenderData as 渲染处理
    participant LabelManager as 标签管理器
    participant Scene as 3D场景

    User->>ClickEvent: 点击节点
    ClickEvent->>LabelManager: clearChildrenLabels()
    ClickEvent->>EnterFocus: handleEnterFocus()
    EnterFocus->>RenderData: renderFocusData()
    RenderData->>LabelManager: clearChildrenLabels()
    Note over RenderData: 执行视角移动和连线动画
    RenderData->>LabelManager: showChildrenLabels(childPoints)
    loop 每个子节点
        LabelManager->>Scene: 创建并添加标签
    end
    LabelManager-->>User: 显示所有子节点标签
```

### 2. 标签生命周期管理

```mermaid
stateDiagram-v2
    [*] --> 未激活
    未激活 --> 显示标签: showChildrenLabels()
    显示标签 --> 隐藏标签: hideChildrenLabels()
    显示标签 --> 清理标签: clearChildrenLabels()
    隐藏标签 --> 显示标签: showChildrenLabels()
    隐藏标签 --> 清理标签: clearChildrenLabels()
    清理标签 --> 未激活
    清理标签 --> 显示标签: showChildrenLabels()
```

## 关键技术实现

### 1. 标签实例管理

```typescript
// 使用Map存储节点ID到标签实例的映射
private activeLabels: Map<string, KsgLabel> = new Map();

// 避免重复创建
if (this.activeLabels.has(point.id)) return;

// 创建新标签实例
const label = new KsgLabel();
label.display(point);
this.activeLabels.set(point.id, label);
```

### 2. 场景集成

```typescript
// 添加到3D场景
ctx.viewGroup.add(label);

// 从场景移除
ctx.viewGroup.remove(label);
```

### 3. 内存管理

```typescript
// 完整清理流程
clearChildrenLabels() {
  this.activeLabels.forEach((label, pointId) => {
    label.hide();                    // 隐藏标签
    ctx.viewGroup!.remove(label);    // 从场景移除
  });
  this.activeLabels.clear();         // 清空集合
}
```

## 集成点说明

### 1. renderFocusData 函数

**位置**：`KsgMap/src/components/ksgMap/core/renderData.ts`

**修改内容**：
- 在聚焦渲染开始时清理旧的子节点标签
- 在连线动画完成后显示新的子节点标签

### 2. enterGlobalView 函数

**位置**：`KsgMap/src/components/ksgMap/core/enterGlobalView.ts`

**修改内容**：
- 在进入全局视图时清理所有子节点标签

### 3. 事件处理函数

**位置**：`KsgMap/src/components/ksgMap/config/event.ts`

**修改内容**：
- 在点击事件处理中清理旧标签
- 在回退函数中清理旧标签

## 优势特点

### 1. 性能优化
- **避免重复创建**：检查标签是否已存在，避免重复创建
- **批量操作**：支持批量显示和清理标签
- **内存管理**：及时清理不需要的标签实例

### 2. 用户体验
- **信息丰富**：同时显示聚焦节点和所有子节点的标签
- **视觉一致**：子节点标签与聚焦节点标签使用相同的样式
- **动画流畅**：标签显示与聚焦动画协调进行

### 3. 代码质量
- **职责分离**：专门的管理器类负责子节点标签
- **接口清晰**：提供简洁的API接口
- **错误处理**：安全处理各种边界情况

## 测试覆盖

### 1. 单元测试
- 标签创建和显示功能
- 标签隐藏和清理功能
- 重复操作处理
- 边界情况处理

### 2. 集成测试
- 与聚焦系统的集成
- 与场景渲染的集成
- 生命周期管理

## 使用示例

```typescript
// 显示子节点标签
focusChildrenLabelManager.showChildrenLabels(childPoints);

// 隐藏子节点标签
focusChildrenLabelManager.hideChildrenLabels();

// 清理子节点标签
focusChildrenLabelManager.clearChildrenLabels();

// 更新标签位置
focusChildrenLabelManager.updateChildLabelPosition(pointId, newPosition);
```

## 文件结构

```
KsgMap/
├── src/components/ksgMap/
│   ├── core/
│   │   ├── FocusChildrenLabelManager.ts  # 新增：子节点标签管理器
│   │   ├── renderData.ts                 # 修改：集成标签显示逻辑
│   │   ├── enterGlobalView.ts           # 修改：添加标签清理
│   │   └── KsgLabel.ts                  # 现有：标签基础类
│   ├── config/
│   │   └── event.ts                     # 修改：添加标签清理逻辑
│   └── types/
│       └── index.ts                     # 现有：类型定义
├── docs/
│   └── 子节点标签显示功能实现说明.md    # 新增：功能说明文档
└── test/
    └── focusChildrenLabels.test.ts      # 新增：单元测试
```

## 配置选项

### 启用/禁用功能
```typescript
// 禁用子节点标签功能
focusChildrenLabelManager.setEnabled(false);

// 启用子节点标签功能
focusChildrenLabelManager.setEnabled(true);
```

### 标签样式自定义
子节点标签继承 `KsgLabel` 的所有样式配置，可以通过CSS进行自定义：

```css
.css2d-label {
  /* 子节点标签的基础样式 */
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.css2d-label-inner {
  /* 标签内容样式 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
```

## 性能考虑

### 1. 标签数量限制
当子节点数量过多时，可以考虑添加显示数量限制：

```typescript
showChildrenLabels(childrenPoints: Point[], maxCount: number = 10) {
  const limitedPoints = childrenPoints.slice(0, maxCount);
  // ... 显示逻辑
}
```

### 2. 延迟加载
对于大量子节点，可以实现延迟加载机制：

```typescript
showChildrenLabelsWithDelay(childrenPoints: Point[], delay: number = 100) {
  childrenPoints.forEach((point, index) => {
    setTimeout(() => {
      this.showSingleChildLabel(point);
    }, index * delay);
  });
}
```

## 扩展功能

### 1. 标签交互
可以为子节点标签添加点击事件：

```typescript
private createInteractiveLabel(point: Point): KsgLabel {
  const label = new KsgLabel();
  label.element.addEventListener('click', () => {
    // 处理子节点标签点击事件
    this.handleChildLabelClick(point);
  });
  return label;
}
```

### 2. 动画效果
可以为标签显示添加动画效果：

```typescript
showChildrenLabelsWithAnimation(childrenPoints: Point[]) {
  childrenPoints.forEach((point, index) => {
    const label = new KsgLabel();
    label.display(point);

    // 添加进入动画
    label.element.style.animation = `labelFadeIn 0.3s ease-out ${index * 0.1}s both`;

    this.activeLabels.set(point.id, label);
    ctx.viewGroup?.add(label);
  });
}
```

## 故障排除

### 常见问题

1. **标签不显示**
   - 检查 `ctx.viewGroup` 是否存在
   - 确认子节点数据是否正确
   - 验证标签管理器是否启用

2. **内存泄漏**
   - 确保在适当时机调用 `clearChildrenLabels()`
   - 检查事件监听器是否正确移除

3. **性能问题**
   - 限制同时显示的标签数量
   - 使用对象池复用标签实例

### 调试工具

```typescript
// 添加调试信息
console.log('活跃标签数量:', focusChildrenLabelManager.getActiveLabelCount());
console.log('标签详情:', Array.from(focusChildrenLabelManager.activeLabels.keys()));
```

## 总结

通过引入 `FocusChildrenLabelManager` 类和相应的集成修改，成功实现了点击节点进入聚焦状态时显示所有子元素标签的功能。该实现具有良好的性能、用户体验和代码质量，为知识图谱的交互体验提供了重要的增强。

### 主要成果
- ✅ 实现了子节点标签的统一管理
- ✅ 集成了完整的生命周期管理
- ✅ 提供了灵活的配置选项
- ✅ 确保了良好的性能表现
- ✅ 包含了完整的测试覆盖

### 后续优化方向
- 🔄 添加标签显示数量限制
- 🔄 实现标签的交互功能
- 🔄 优化大量标签的渲染性能
- 🔄 添加更多的动画效果
