import { CSS2DObject } from "three/examples/jsm/renderers/CSS2DRenderer.js";
import type { Point, ViewRange } from "../types";
// import { renderMathJax, reFormateTitle } from "../utils/mathJax";

/**
 * 标签系统模块 - KsgLabel.ts
 *
 * 职责：
 * 1. 为节点提供2D HTML标签显示
 * 2. 智能定位，避免标签超出视口边界
 * 3. 支持MathJax数学公式渲染
 * 4. 管理标签的显示隐藏和动画效果
 * 5. 提供悬停和聚焦两种不同的标签模式
 *
 * 技术特点：
 * - 基于CSS2DRenderer的HTML渲染
 * - 动态位置计算和边界检测
 * - 支持LaTeX数学公式
 * - CSS动画和过渡效果
 * - 响应式布局适配
 */

/**
 * 用于计算标签尺寸的隐藏DOM元素
 *
 * 通过预计算标签尺寸来确定最佳显示位置
 * 避免标签显示后再调整位置造成的闪烁
 */
const computedDom = document.createElement("div");
computedDom.className = "css2d-label-computed";
computedDom.style.opacity = "0"; // 完全透明，不影响视觉
document.body.appendChild(computedDom);

/**
 * 知识节点标签类
 * 继承自Three.js的CSS2DObject，支持3D场景中的2D HTML渲染
 */
export class KsgLabel extends CSS2DObject {
  /** 标签宽度 - 用于位置计算 */
  labelWidth: number = 0;
  /** 标签高度 - 用于位置计算 */
  labelHeight: number = 0;
  /** 当前绑定的节点对象 */
  point: Point | null = null;
  /** 上一次显示的节点索引 - 用于避免重复处理 */
  lastIndex: number | null = null;
  /** 标签相对节点的偏移位置 */
  offset: { x: number; y: number } = { x: 0, y: 0 };

  /**
   * 构造函数 - 初始化标签对象
   *
   * @param offset 标签偏移量 - 相对于节点位置的像素偏移
   */
  constructor(offset: { x: number; y: number } = { x: 10, y: 10 }) {
    // 创建标签的HTML容器元素
    const element = document.createElement("div");
    element.className = "css2d-label";

    // 调用父类构造函数，传入HTML元素
    super(element);

    // 初始状态为隐藏
    this.visible = false;

    // 设置锚点位置为右上角 (0,1)
    this.center.set(0, 1);

    // 保存偏移配置
    this.offset = offset;

    // 通过CSS变量设置偏移量，便于样式控制
    this.element.style.setProperty("--ml", `${offset.x}px`);
    this.element.style.setProperty("--mb", `${offset.y}px`);
  }

  /**
   * 显示标签并绑定到指定节点
   *
   * 执行流程：
   * 1. 检查是否为重复显示（优化性能）
   * 2. 设置标签内容和样式
   * 3. 渲染MathJax数学公式
   * 4. 计算并设置最佳显示位置
   * 5. 显示标签并缓存节点信息
   *
   * @param point 要绑定的节点对象
   * @param option 可选的位置计算参数
   */
  display(
    point: Point,
    option?: {
      viewRange: ViewRange; // 视口范围，用于边界检测
      dnc: { x: number; y: number }; // 设备标准化坐标
    }
  ) {
    // 避免重复显示同一个节点
    if (this.lastIndex === point.index) return;

    // 绑定节点数据
    this.point = point;

    // 设置标签HTML内容，包含格式化的节点名称
    this.element.innerHTML = `<div class='css2d-label-inner'><p>${point.name}</p></div>`;

    // 设置元素ID为节点ID，便于调试和样式控制
    this.element.setAttribute("id", point.id);

    // 设置标签的3D位置与节点坐标一致
    this.position.set(...point.coordinate);

    // 渲染MathJax数学公式（如果内容包含LaTeX）
    // renderMathJax(this.element);

    // 将内容复制到计算DOM中，用于尺寸计算
    computedDom.innerHTML = this.element.innerHTML;

    // 如果提供了位置参数，执行智能定位
    if (option) this.setPosition(option!);

    // 显示标签
    this.visible = true;

    // 缓存当前节点索引
    this.lastIndex = point.index!;
  }

  /**
   * 智能定位算法 - 确保标签不会超出视口边界
   *
   * 算法流程：
   * 1. 计算标签的实际尺寸
   * 2. 检测水平方向的边界冲突
   * 3. 检测垂直方向的边界冲突
   * 4. 根据冲突情况选择最佳位置
   * 5. 应用相应的CSS样式和锚点设置
   *
   * @param option 位置计算参数
   * @returns 最终选择的位置字符串
   */
  private setPosition(option: {
    viewRange: ViewRange;
    dnc: { x: number; y: number };
  }) {
    // 计算标签的实际尺寸（包含偏移量）
    let { width, height } = computedDom.getBoundingClientRect();
    width += this.offset.x;
    height += this.offset.y;

    // 默认位置为右侧显示
    let position = "right";

    // 水平方向边界检测
    if (option.viewRange.minX + option.dnc.x + width >= option.viewRange.maxX) {
      // 右侧空间不足，改为左侧显示
      position = "left";
    } else if (
      option.viewRange.minX + option.dnc.x - width <
      option.viewRange.minX
    ) {
      // 左侧空间不足，保持右侧显示
      position = "right";
    }

    // 垂直方向边界检测
    if (
      option.viewRange.minY + option.dnc.y + height >=
      option.viewRange.maxY
    ) {
      // 下方空间不足，改为上方显示
      position += "top";
    } else if (
      option.viewRange.minY + option.dnc.y - height <
      option.viewRange.minY
    ) {
      // 上方空间不足，改为下方显示
      position += "bottom";
    } else {
      // 默认为上方显示
      position += "top";
    }

    // 根据最终位置设置CSS样式和锚点
    switch (position) {
      case "lefttop":
        // 左上角显示：锚点在右下角
        this.center.set(1, 1);
        this.element.style.setProperty("--mt", "0px");
        this.element.style.setProperty("--ml", "0px");
        this.element.style.setProperty("--mr", `${this.offset.x}px`);
        this.element.style.setProperty("--mb", `${this.offset.y}px`);
        break;
      case "leftbottom":
        // 左下角显示：锚点在右上角
        this.center.set(1, 0);
        this.element.style.setProperty("--mb", "0px");
        this.element.style.setProperty("--ml", "0px");
        this.element.style.setProperty("--mr", `${this.offset.x}px`);
        this.element.style.setProperty("--mt", `${this.offset.y}px`);
        break;
      case "righttop":
        // 右上角显示：锚点在左下角
        this.center.set(0, 1);
        this.element.style.setProperty("--mt", "0px");
        this.element.style.setProperty("--mr", "0px");
        this.element.style.setProperty("--ml", `${this.offset.x}px`);
        this.element.style.setProperty("--mb", `${this.offset.y}px`);
        break;
      case "rightbottom":
        // 右下角显示：锚点在左上角
        this.center.set(0, 0);
        this.element.style.setProperty("--mr", "0px");
        this.element.style.setProperty("--mb", "0px");
        this.element.style.setProperty("--ml", `${this.offset.x}px`);
        this.element.style.setProperty("--mt", `${this.offset.y}px`);
        break;
    }

    return position;
  }

  /**
   * 隐藏标签并清理状态
   *
   * 在鼠标移出节点或切换标签目标时调用
   */
  hide() {
    this.visible = false;
    this.point = null;
    this.userData = {};
    this.lastIndex = null;
  }

  /**
   * 距离控制显示效果
   *
   * 根据相机距离动态调整标签的可见性
   * 近距离时显示，远距离时隐藏，提供更好的视觉体验
   *
   * @param show 是否显示标签
   */
  distanceShow(show: boolean) {
    this.element.style.setProperty(
      "--animation",
      show ? "label-enter" : "label-leave"
    );
  }

  /**
   * 更新标签位置
   *
   * 用于节点位置发生变化时同步标签位置
   * 通常在动画过程中调用
   *
   * @param position 新的3D坐标 [x, y, z]
   */
  updatePosition(position: [number, number, number]) {
    this.position.set(...position);
  }
}

// 创建两个全局标签实例
/** 悬停标签 - 用于鼠标悬停时显示节点信息 */
const hoverLabel = new KsgLabel();

/** 聚焦标签 - 用于聚焦状态下显示节点信息 */
const focusLabel = new KsgLabel();

export { hoverLabel, focusLabel };
